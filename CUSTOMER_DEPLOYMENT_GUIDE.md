# HTTPS流量抓包工具 - 客户现场部署指南

## 🚀 工具概述

这是一个便携式HTTPS流量抓包工具，专门用于客户现场快速部署、抓包分析。工具能够：

- ✅ **自动注入SSL密钥提取器**：无需修改应用启动参数
- ✅ **实时抓包**：支持自定义时间、接口、过滤条件
- ✅ **自动清理**：抓包完成后自动清理注入环境
- ✅ **一键打包**：生成包含PCAP和密钥的完整分析包
- ✅ **Wireshark兼容**：直接支持Wireshark解密分析

## 📦 部署包内容

```
https_capture_toolkit_YYYYMMDD_HHMMSS.tar.gz
├── quick_deploy.sh              # 一键部署脚本
├── portable_capture.sh          # 主要抓包工具
├── oneclick_capture.sh          # 一键运行脚本
├── keylog_injector.c            # SSL密钥注入器源码
├── Makefile                     # 编译配置
├── setup_global_injection.sh    # 注入管理脚本
└── DEPLOYMENT_GUIDE.md          # 详细使用说明
```

## 🎯 快速开始

### 方法一：标准部署（推荐）

```bash
# 1. 上传工具包到目标服务器
scp https_capture_toolkit_*.tar.gz user@target-server:/tmp/

# 2. 在目标服务器上解压
ssh user@target-server
cd /tmp
tar -xzf https_capture_toolkit_*.tar.gz
cd https_capture_toolkit_*/

# 3. 一键部署（自动安装依赖并编译）
sudo ./quick_deploy.sh

# 4. 开始抓包
sudo ./portable_capture.sh 60    # 抓包60秒
```

### 方法二：一键运行

```bash
# 解压后直接运行（包含部署+抓包）
sudo ./oneclick_capture.sh 120   # 部署并抓包120秒
```

## 📋 抓包参数说明

```bash
./portable_capture.sh [时间] [接口] [过滤条件]
```

### 参数详解

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| 时间 | 抓包持续时间（秒） | 60 | `120` |
| 接口 | 网络接口名称 | any | `eth0`, `ens160` |
| 过滤条件 | tcpdump过滤表达式 | `port 443 or port 8443 or port 9443` | `"port 443"` |

### 使用示例

```bash
# 基础抓包（60秒，所有HTTPS端口）
sudo ./portable_capture.sh

# 抓包2分钟
sudo ./portable_capture.sh 120

# 在指定接口抓包5分钟
sudo ./portable_capture.sh 300 eth0

# 只抓443端口流量
sudo ./portable_capture.sh 180 any "port 443"

# 抓取特定主机的HTTPS流量
sudo ./portable_capture.sh 240 any "host ************* and port 443"
```

## 📁 输出文件说明

抓包完成后会生成以下文件：

```
capture_YYYYMMDD_HHMMSS/
├── https_traffic_YYYYMMDD_HHMMSS.pcap    # 网络流量包
├── ssl_keylog_YYYYMMDD_HHMMSS.txt        # SSL密钥文件
└── capture_report_YYYYMMDD_HHMMSS.txt    # 抓包分析报告

https_capture_YYYYMMDD_HHMMSS.tar.gz      # 打包文件（用于下载）
```

### 文件用途

- **PCAP文件**：包含加密的HTTPS网络流量，可用Wireshark分析
- **密钥文件**：包含SSL/TLS密钥，用于解密HTTPS流量
- **报告文件**：包含抓包统计信息和使用说明
- **打包文件**：便于下载传输的压缩包

## 🔓 Wireshark解密步骤

### 1. 下载文件到本地
```bash
scp user@server:/tmp/https_capture_*.tar.gz ./
tar -xzf https_capture_*.tar.gz
```

### 2. 在Wireshark中配置SSL密钥

1. 打开Wireshark
2. 菜单：`Edit` → `Preferences`
3. 展开：`Protocols` → `TLS`
4. 找到：`(Pre)-Master-Secret log filename`
5. 浏览选择：`ssl_keylog_YYYYMMDD_HHMMSS.txt`
6. 点击：`OK`

### 3. 打开PCAP文件
- 菜单：`File` → `Open`
- 选择：`https_traffic_YYYYMMDD_HHMMSS.pcap`

### 4. 查看解密内容
- 成功解密的流量会显示HTTP协议内容
- 可以看到明文的HTTP请求和响应
- 右键数据包选择"Follow HTTP Stream"查看完整对话

## ✅ 支持的应用类型

### 🟢 完全支持（使用OpenSSL）
- **Web服务器**：Nginx, Apache HTTP Server
- **命令行工具**：curl, wget
- **编程语言**：Python requests, C/C++应用
- **数据库**：MySQL, PostgreSQL (SSL连接)
- **代理服务**：HAProxy, Squid

### 🔴 不支持
- **Java应用**：Tomcat, Spring Boot (使用JSSE)
- **Node.js应用**：Express, Koa (内置TLS)
- **Go应用**：使用标准库crypto/tls
- **.NET应用**：ASP.NET Core
- **Python内置**：某些使用内置SSL的库

## 🛠️ 故障排除

### 1. 编译失败
```bash
# CentOS/RHEL/Rocky Linux
sudo yum install -y gcc make openssl-devel

# Ubuntu/Debian
sudo apt update && sudo apt install -y gcc make libssl-dev

# 重新编译
make clean && make
```

### 2. 无SSL密钥记录
**可能原因**：
- 目标应用不使用OpenSSL
- 抓包期间没有HTTPS流量
- 应用使用了连接池/长连接

**解决方法**：
```bash
# 检查应用是否加载了注入器
pmap $(pgrep nginx) | grep keylog

# 强制重新建立连接
systemctl restart target-application

# 生成测试流量
curl -k https://localhost:443/
```

### 3. 权限问题
```bash
# 确保使用root权限
sudo ./portable_capture.sh

# 检查SELinux状态
getenforce
# 如果是Enforcing，临时禁用
sudo setenforce 0
```

### 4. 网络接口问题
```bash
# 查看可用网络接口
ip addr show

# 使用具体接口名称
sudo ./portable_capture.sh 60 ens160
```

## 🔒 安全注意事项

1. **临时修改系统配置**：工具会临时修改`/etc/ld.so.preload`
2. **自动清理**：抓包完成后会自动清理注入环境
3. **敏感数据**：密钥文件包含敏感信息，请妥善保管
4. **测试建议**：建议先在测试环境验证
5. **权限要求**：需要root权限运行

## 📞 技术支持

如遇问题，请提供以下信息：

1. **系统信息**：
   ```bash
   cat /etc/os-release
   uname -a
   ```

2. **错误日志**：完整的错误输出信息

3. **目标应用**：应用类型、版本、配置信息

4. **网络环境**：网络拓扑、防火墙配置

## 📝 使用检查清单

### 部署前检查
- [ ] 确认目标服务器有root权限
- [ ] 确认网络连接正常
- [ ] 确认目标应用正在运行
- [ ] 确认有足够磁盘空间（建议>100MB）

### 抓包前检查
- [ ] 确认目标应用使用OpenSSL
- [ ] 确认抓包时间合理（建议1-5分钟）
- [ ] 确认会有HTTPS流量产生
- [ ] 确认网络接口选择正确

### 抓包后检查
- [ ] 确认生成了PCAP文件
- [ ] 确认SSL密钥文件有内容
- [ ] 确认打包文件完整
- [ ] 确认环境已清理

---

**版本**：v1.0  
**更新时间**：2025-08-28  
**适用系统**：CentOS/RHEL 7+, Ubuntu 18+, Debian 9+
